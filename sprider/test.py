import requests
import json
import gzip
from io import BytesIO

def request_google_translate_api():
    """
    请求Google翻译API接口获取数据
    """
    # API配置
    url = "https://translate-pa.googleapis.com/v1/translateHtml"
    api_key = "AIzaSyATBXajvzQLTDHEQbcpq0Ihe0vWDHmO520"

    # 请求头
    headers = {
        'accept': '*/*',
        'accept-encoding': 'gzip, deflate, br, zstd',
        'accept-language': 'zh-CN,zh;q=0.9',
        'content-type': 'application/json+protobuf',
        'origin': 'https://apps.fldfs.com',
        'referer': 'https://apps.fldfs.com/',
        'sec-ch-ua': '"Google Chrome";v="137", "Chromium";v="137", "Not/A)Brand";v="24"',
        'sec-ch-ua-mobile': '?0',
        'sec-ch-ua-platform': '"macOS"',
        'sec-fetch-dest': 'empty',
        'sec-fetch-mode': 'cors',
        'sec-fetch-site': 'cross-site',
        'user-agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
        'x-client-data': 'CJa2yQEIprbJAQipncoBCLXuygEIk6HLAQiRo8sBCIagzQEIk/bOAQ==',
        'x-goog-api-key': api_key
    }

    # 构建请求数据 - 根据你提供的负载信息
    request_data = [
        [
            [
                "Direct premium written for policies in force that include wind coverage",
                "Direct premium written for policies in force that exclude wind coverage",
                "Number of policies transferred to other insurers",
                "Number of policies received from other insurers",
                "Claims Opened",
                "Claims Closed",
                "Claims Pending",
                "Claims with Alt Dispute Resolution",
                "Claims with Mediation",
                "Claims with Arbitration",
                "Claims with Appraisal",
                "Claims with Sink Hole Eval",
                "Claims with Settlement",
                "Claims with Other",
                "Commercial Residential - Allied Lines (Condo Associations Only)",
                "140",
                "5",
                "6",
                "1",
                "8",
                "1,209,744,508",
                "4,914,888",
                "Commercial Residential - Allied Lines (Excl Condo Associations)",
                "47",
                "2",
                "3",
                "40,255,063",
                "248,782",
                "Commercial Residential - CMP (Condo Associations Only)",
                "36",
                "21",
                "218,633,830",
                "1,412,579",
                "15",
                "99,838,817",
                "1,212,250",
                "200,329",
                "Commercial Residential - CMP (Excl Condo Associations)",
                "632",
                "14",
                "65",
                "75",
                "117,664,403",
                "769,317",
                "557",
                "28,261,472",
                "690,163",
                "79,154",
                "Commercial Residential - Dwelling/Fire (Condo Associations Only)",
                "29",
                "4",
                "121,420,700",
                "873,259",
                "25",
                "78,688,000",
                "802,367",
                "70,892",
                "Commercial Residential - Dwelling/Fire (Excl Condo Associations)",
                "30",
                "51,083,400",
                "383,825",
                "45,000",
                "383,567",
                "258",
                "Personal Residential - Allied Lines",
                "7,847",
                "227",
                "1,158",
                "648",
                "34",
                "1,457,826,862",
                "8,451,361",
                "7,813",
                "4,784,645",
                "8,433,446",
                "17,915",
                "Personal Residential - Condominium Unit Owners",
                "8,849",
                "184",
                "505",
                "2,503",
                "575,773,997",
                "4,842,478",
                "6,346",
                "197,872,453",
                "3,909,620",
                "932,858",
                "138",
                "164",
                "Personal Residential - Dwelling/Fire",
                "9,398",
                "355",
                "169",
                "643",
                "2,072",
                "1,286,593,850",
                "9,304,016",
                "7,326",
                "487,967,576",
                "7,703,919",
                "1,600,098",
                "100",
                "388",
                "Personal Residential - Farmowners",
                "953,700",
                "3,525",
                "Personal Residential - Homeowners (Excl Tenant and Condo) - Owner Occupied",
                "34,584",
                "990",
                "507",
                "1,508",
                "2,338",
                "12,182,554,127",
                "59,020,178",
                "32,246",
                "1,074,347,589",
                "56,638,865",
                "2,381,314",
                "84",
                "135",
                "Personal Residential - Mobile Homeowners",
                "4,929",
                "167",
                "160",
                "333",
                "281",
                "315,088,588",
                "4,607,828",
                "4,648",
                "13,737,253",
                "4,491,156",
                "116,672",
                "9",
                "10",
                "Personal Residential - Tenants",
                "3,716",
                "357",
                "33",
                "520",
                "20",
                "130,865,328",
                "915,249",
                "3,696",
                "955,420",
                "910,288",
                "4,961",
                "12",
                "Total",
                "70,239",
                "2,380",
                "2,292",
                "4,179",
                "7,349",
                "17,708,458,355",
                "95,747,285",
                "62,890",
                "1,986,498,225",
                "90,342,835",
                "5,404,450",
                "341",
                "709",
                "Period Ending  6/30/2009",
                "Policies in force",
                "Number of policies canceled",
                "Number of policies nonrenewed",
                "Number of policies canceled due to hurricane risk",
                "Number of policies nonrenewed due to hurricane risk",
                "Number of new policies written",
                "Policies in force that exclude wind coverage",
                "Total $ value of exposure for policies in force that include wind coverage",
                "Total premiums written",
                "Policies in force that include wind coverage",
                "Total $ value of exposure for policies in force that exclude wind coverage",
                "105",
                "898,006,386",
                "3,493,400",
                "39",
                "36,360,183",
                "218,989",
                "226,320,985",
                "1,407,064",
                "16",
                "99,165,949",
                "1,227,388",
                "179,676",
                "594",
                "23",
                "17",
                "64",
                "113,251,172",
                "732,770",
                "530",
                "26,620,584",
                "631,902",
                "100,868",
                "32",
                "112,174,600",
                "824,489",
                "27",
                "82,870,800",
                "746,041",
                "78,448",
                "57,049,300",
                "392,342",
                "392,084",
                "6,905",
                "225",
                "2,495",
                "1,776",
                "37",
                "1,258,379,848",
                "6,877,278",
                "6,868",
                "5,199,894",
                "6,859,898",
                "17,380",
                "8,883",
                "262",
                "273",
                "561",
                "2,445",
                "584,225,635",
                "4,871,193",
                "6,438",
                "192,508,826",
                "3,963,322",
                "907,871",
                "62",
                "71",
                "9,255",
                "429",
                "206",
                "502",
                "1,975",
                "1,282,116,850",
                "9,340,550",
                "7,280",
                "469,150,109",
                "7,831,224",
                "1,509,327",
                "35,270",
                "1,010",
                "474",
                "2,156",
                "2,248",
                "12,600,460,352",
                "60,271,563",
                "33,022",
                "1,017,529,518",
                "58,025,609",
                "2,245,954",
                "31",
                "42",
                "4,936",
                "159",
                "185",
                "338",
                "243",
                "316,323,933",
                "4,507,096",
                "4,693",
                "12,079,286",
                "4,408,334",
                "98,762",
                "115",
                "121",
                "3,895",
                "407",
                "43",
                "18",
                "132,354,243",
                "921,232"
            ],
            "en",
            "zh-CN"
        ],
        "te_lib"
    ]

    try:
        print("🚀 开始请求Google翻译API...")
        print(f"📡 请求URL: {url}")

        # 发送POST请求
        response = requests.post(
            url,
            headers=headers,
            json=request_data,
            timeout=30
        )

        print(f"📊 响应状态码: {response.status_code}")
        print(f"📋 响应头: {dict(response.headers)}")

        if response.status_code == 200:
            print("✅ 请求成功!")

            # 检查是否是gzip压缩的响应
            if response.headers.get('content-encoding') == 'gzip':
                print("🗜️ 响应数据已压缩，正在解压...")
                # 解压gzip数据
                compressed_data = BytesIO(response.content)
                with gzip.GzipFile(fileobj=compressed_data) as f:
                    decompressed_data = f.read().decode('utf-8')
                print("📄 解压后的响应数据:")
                print(decompressed_data)

                # 尝试解析JSON
                try:
                    json_data = json.loads(decompressed_data)
                    print("📊 解析后的JSON数据:")
                    print(json.dumps(json_data, indent=2, ensure_ascii=False))
                    return json_data
                except json.JSONDecodeError as e:
                    print(f"❌ JSON解析失败: {e}")
                    print("📄 原始响应内容:")
                    print(decompressed_data)
                    return decompressed_data
            else:
                print("📄 响应数据:")
                response_text = response.text
                print(response_text)

                # 尝试解析JSON
                try:
                    json_data = response.json()
                    print("📊 解析后的JSON数据:")
                    print(json.dumps(json_data, indent=2, ensure_ascii=False))
                    return json_data
                except json.JSONDecodeError as e:
                    print(f"❌ JSON解析失败: {e}")
                    return response_text
        else:
            print(f"❌ 请求失败，状态码: {response.status_code}")
            print(f"📄 错误响应: {response.text}")
            return None

    except requests.exceptions.RequestException as e:
        print(f"❌ 请求异常: {e}")
        return None
    except Exception as e:
        print(f"❌ 其他错误: {e}")
        return None


def save_response_to_file(data, filename="google_translate_response.json"):
    """
    将响应数据保存到文件
    """
    try:
        with open(filename, 'w', encoding='utf-8') as f:
            if isinstance(data, dict) or isinstance(data, list):
                json.dump(data, f, indent=2, ensure_ascii=False)
            else:
                f.write(str(data))
        print(f"💾 数据已保存到文件: {filename}")
    except Exception as e:
        print(f"❌ 保存文件失败: {e}")


if __name__ == "__main__":
    print("🔧 Google翻译API请求工具")
    print("=" * 50)

    # 请求API
    result = request_google_translate_api()

    if result:
        # 保存响应数据
        save_response_to_file(result)
        print("\n✅ 任务完成!")
    else:
        print("\n❌ 任务失败!")