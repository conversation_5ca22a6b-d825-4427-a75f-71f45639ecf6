# ========================================================================
# 🔧 恢复单个县区选择：保险公司×县区的完整组合处理
# ========================================================================

# ==== 本地配置 ====
CHROME_DRIVER_PATH = "/Users/<USER>/Documents/赚点小银子/编程/400/chromedriver"
DOWNLOAD_DIR = "/Users/<USER>/Documents/赚点小银子/编程/400/QSRNG_reports"

import time
import os
from selenium import webdriver
from selenium.webdriver.chrome.service import Service
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait, Select
from selenium.webdriver.support import expected_conditions as EC
import requests
from selenium.common.exceptions import JavascriptException, TimeoutException

os.makedirs(DOWNLOAD_DIR, exist_ok=True)

# ==== 保险公司列表 ====


# ==== 手动创建的静态列表 ====
insurers = [
    ("101268", "21ST CENTURY CENTENNIAL INSURANCE COMPANY"),
    ("100933", "21ST CENTURY NORTH AMERICA INSURANCE COMPANY"),
    ("101109", "21ST CENTURY PREMIER INSURANCE COMPANY"),
    ("105540", "ACCELERANT NATIONAL INSURANCE COMPANY"),
    ("200873", "ACCIDENT FUND INSURANCE COMPANY OF AMERICA"),
    ("102970", "ACCREDITED SURETY AND CASUALTY COMPANY, INC."),
    ("104790", "ACE AMERICAN INSURANCE COMPANY"),
    ("101278", "ACE EMPLOYERS INSURANCE COMPANY"),
    ("100760", "ACE FIRE UNDERWRITERS INSURANCE COMPANY"),
    ("201865", "ACE INSURANCE COMPANY OF THE MIDWEST"),
    ("100634", "ACE PROPERTY AND CASUALTY INSURANCE COMPANY"),
    ("101736", "ACSTAR INSURANCE COMPANY"),
    ("105484", "ADDISON INSURANCE COMPANY"),
    ("203679", "ADMIRAL INDEMNITY COMPANY"),
    ("101266", "AEGIS SECURITY INSURANCE COMPANY"),
    ("100619", "AFFILIATED FM INSURANCE COMPANY"),
    ("101521", "AFFIRMATIVE INSURANCE COMPANY"),
    ("101911", "AGCS MARINE INSURANCE COMPANY"),
    ("102216", "AGRI GENERAL INSURANCE COMPANY"),
    ("100502", "AIG ASSURANCE COMPANY"),
    ("101350", "AIG PROPERTY CASUALTY COMPANY"),
    ("100999", "AIU INSURANCE COMPANY"),
    ("100688", "ALEA NORTH AMERICA INSURANCE COMPANY"),
    ("104824", "ALLIANZ GLOBAL RISKS US INSURANCE COMPANY"),
    ("201024", "ALLIED INSURANCE COMPANY OF AMERICA"),
    ("102215", "ALLIED PROPERTY &amp; CASUALTY INSURANCE COMPANY"),
    ("100711", "ALLIED WORLD INSURANCE COMPANY"),
    ("201363", "ALLIED WORLD NATIONAL ASSURANCE COMPANY"),
    ("102607", "ALLIED WORLD SPECIALTY INSURANCE COMPANY"),
    ("101301", "ALLMERICA FINANCIAL BENEFIT INSURANCE COMPANY"),
    ("104726", "ALLSTATE FIRE AND CASUALTY INSURANCE COMPANY"),
    ("101926", "ALLSTATE INDEMNITY COMPANY"),
    ("101641", "ALLSTATE INSURANCE COMPANY"),
    ("101770", "ALLSTATE NORTHBROOK INDEMNITY COMPANY"),
    ("101799", "ALLSTATE PROPERTY &amp; CASUALTY INSURANCE COMPANY"),
    ("102077", "ALPHA PROPERTY &amp; CASUALTY INSURANCE COMPANY"),
    ("201491", "AMERICAN AGRI-BUSINESS INSURANCE COMPANY"),
    ("102519", "AMERICAN ALTERNATIVE INSURANCE CORPORATION"),
    ("101098", "AMERICAN AUTOMOBILE INSURANCE COMPANY"),
    ("102808", "AMERICAN BANKERS INSURANCE COMPANY OF FLORIDA"),
    ("102620", "AMERICAN BUILDERS INSURANCE COMPANY"),
    ("202614", "AMERICAN CAPITAL ASSURANCE CORP"),
    ("201406", "AMERICAN CAPITAL ASSURANCE CORP."),
    ("101175", "AMERICAN CASUALTY COMPANY OF READING, PENNSYLVANIA"),
    ("201598", "AMERICAN COASTAL INSURANCE COMPANY"),
    ("101321", "AMERICAN COLONIAL INSURANCE COMPANY"),
    ("101461", "AMERICAN COMMERCE INSURANCE COMPANY"),
    ("101578", "AMERICAN ECONOMY INSURANCE COMPANY"),
    ("105174", "AMERICAN EQUITY SPECIALTY INSURANCE COMPANY"),
    ("200896", "AMERICAN FAMILY CONNECT PROPERTY &amp; CASUALTY INSURANCE COMPANY"),
    ("101383", "AMERICAN FAMILY HOME INSURANCE COMPANY"),
    ("102793", "AMERICAN FIRE AND CASUALTY COMPANY"),
    ("102962", "AMERICAN GENERAL PROPERTY INS CO OF FLORIDA"),
    ("101922", "AMERICAN GUARANTEE AND LIABILITY INSURANCE COMPANY"),
    ("204617", "AMERICAN GUARDIAN SHIELD INSURANCE COMPANY"),
    ("201485", "AMERICAN HALLMARK INSURANCE COMPANY OF TEXAS"),
    ("100990", "AMERICAN HOME ASSURANCE COMPANY"),
    ("201293", "AMERICAN INTEGRITY INSURANCE COMPANY"),
    ("201388", "AMERICAN KEYSTONE INSURANCE COMPANY"),
    ("104316", "AMERICAN MERCURY INSURANCE COMPANY"),
    ("204653", "AMERICAN MOBILE INSURANCE EXCHANGE"),
    ("101384", "AMERICAN MODERN HOME INSURANCE COMPANY"),
    ("201035", "AMERICAN MODERN INSURANCE COMPANY OF FLORIDA, INC."),
    ("203279", "AMERICAN MODERN PROPERTY AND CASUALTY INSURANCE COMPANY"),
    ("201252", "AMERICAN MODERN SELECT INSURANCE COMPANY"),
    ("102279", "AMERICAN NATIONAL GENERAL INSURANCE COMPANY"),
    ("102266", "AMERICAN NATIONAL PROPERTY &amp; CASUALTY COMPANY"),
    ("201909", "AMERICAN PLATINUM PROPERTY AND CASUALTY INSURANCE COMPANY"),
    ("102719", "AMERICAN PROPERTY INSURANCE COMPANY"),
    ("102112", "AMERICAN RELIABLE INSURANCE COMPANY"),
    ("101988", "AMERICAN ROAD INSURANCE COMPANY (THE)"),
    ("102770", "AMERICAN SAFETY CASUALTY INSURANCE COMPANY"),
    ("102729", "AMERICAN SECURITY INSURANCE COMPANY"),
    ("103217", "AMERICAN SOUTHERN HOME INSURANCE COMPANY"),
    ("102789", "AMERICAN SOUTHERN INSURANCE COMPANY"),
    ("101546", "AMERICAN STATES INSURANCE COMPANY"),
    ("103656", "AMERICAN STRATEGIC INSURANCE CORP."),
    ("102113", "AMERICAN SUMMIT INSURANCE COMPANY"),
    ("201040", "AMERICAN TRADITIONS INSURANCE COMPANY"),
    ("101784", "AMERICAN ZURICH INSURANCE COMPANY"),
    ("101997", "AMERISURE INSURANCE COMPANY"),
    ("101972", "AMERISURE MUTUAL INSURANCE COMPANY"),
    ("202059", "AMERISURE PARTNERS INSURANCE COMPANY"),
    ("101750", "AMEX ASSURANCE COMPANY"),
    ("200488", "AMGUARD INSURANCE COMPANY"),
    ("100625", "AMICA MUTUAL INSURANCE COMPANY"),
    ("102066", "ARCH INDEMNITY INSURANCE COMPANY"),
    ("102265", "ARCH INSURANCE COMPANY"),
    ("101930", "ARGONAUT GREAT CENTRAL INSURANCE COMPANY"),
    ("104711", "ARGONAUT INSURANCE COMPANY"),
    ("101706", "ARGONAUT-MIDWEST INSURANCE COMPANY"),
    ("101885", "ARGUS FIRE &amp; CASUALTY INSURANCE COMPANY"),
    ("102404", "ARMED FORCES INSURANCE EXCHANGE"),
    ("101003", "ARROWOOD INDEMNITY COMPANY"),
    ("200813", "ASCOT INSURANCE COMPANY"),
    ("200844", "ASI ASSURANCE CORP."),
    ("201044", "ASI HOME INSURANCE CORP."),
    ("201764", "ASI PREFERRED INSURANCE CORP."),
    ("204361", "ASI SELECT INSURANCE CORP."),
    ("104494", "ASPEN AMERICAN INSURANCE COMPANY"),
    ("102843", "ASSOCIATED INDUSTRIES INSURANCE COMPANY, INC."),
    ("105252", "ASSOCIATION CASUALTY INSURANCE COMPANY"),
    ("101032", "ASSURANCE COMPANY OF AMERICA"),
    ("200951", "ASSURANCEAMERICA INSURANCE COMPANY"),
    ("201139", "ATAIN INSURANCE COMPANY"),
    ("100491", "ATHOME INSURANCE COMPANY"),
    ("100981", "ATLANTIC MUTUAL INSURANCE COMPANY"),
    ("105329", "ATLANTIC SPECIALTY INSURANCE COMPANY"),
    ("201428", "AUSTIN MUTUAL INSURANCE COMPANY"),
    ("201397", "AUTO CLUB INSURANCE COMPANY OF FLORIDA"),
    ("103533", "AUTO CLUB SOUTH INSURANCE COMPANY"),
    ("101957", "AUTO-OWNERS INSURANCE COMPANY"),
    ("201552", "AVATAR PROPERTY &amp; CASUALTY INSURANCE COMPANY"),
    ("102450", "AVEMCO INSURANCE COMPANY"),
    ("100072", "AVENTUS INSURANCE COMPANY"),
    ("100955", "AXA INSURANCE COMPANY"),
    ("104345", "AXA XL INSURANCE COMPANY AMERICAS"),
    ("102074", "AXIS INSURANCE COMPANY"),
    ("100957", "AXIS REINSURANCE COMPANY"),
    ("103050", "BANKERS INSURANCE COMPANY"),
    ("104511", "BANKERS STANDARD FIRE AND MARINE COMPANY"),
    ("102968", "BANKERS STANDARD INSURANCE COMPANY"),
    ("100582", "BEAZLEY INSURANCE COMPANY, INC."),
    ("101216", "BEDIVERE INSURANCE COMPANY"),
    ("102417", "BENCHMARK INSURANCE COMPANY"),
    ("203657", "BERKLEY CASUALTY COMPANY"),
    ("102369", "BERKLEY INSURANCE COMPANY"),
    ("202451", "BERKLEY NATIONAL INSURANCE COMPANY"),
    ("102294", "BERKLEY REGIONAL INSURANCE COMPANY"),
    ("102425", "BERKSHIRE HATHAWAY DIRECT INSURANCE COMPANY"),
    ("103808", "BERKSHIRE HATHAWAY SPECIALTY INSURANCE COMPANY"),
    ("101646", "BITCO GENERAL INSURANCE CORPORATION"),
    ("101917", "BITCO NATIONAL INSURANCE COMPANY"),
    ("204940", "BRANCH INSURANCE EXCHANGE"),
    ("202766", "BRIERFIELD INSURANCE COMPANY"),
    ("101548", "BROTHERHOOD MUTUAL INSURANCE COMPANY"),
    ("102680", "BUILDERS ALLIANCE INSURANCE COMPANY"),
    ("202549", "BUILDERS MUTUAL INSURANCE COMPANY"),
    ("105401", "CALIFORNIA CASUALTY INDEMNITY EXCHANGE"),
    ("102638", "CANAL INSURANCE COMPANY"),
    ("103454", "CAPACITY INSURANCE COMPANY"),
    ("102055", "CAPITOL INDEMNITY CORPORATION"),
    ("103667", "CAPITOL PREFERRED INSURANCE COMPANY, INC."),
    ("102849", "CAROLINA CASUALTY INSURANCE COMPANY"),
    ("101904", "CASTLE KEY INDEMNITY COMPANY"),
    ("101849", "CASTLE KEY INSURANCE COMPANY"),
    ("101285", "CASTLEPOINT NATIONAL INSURANCE COMPANY"),
    ("102252", "CATERPILLAR INSURANCE COMPANY"),
    ("100959", "CEDAR INSURANCE COMPANY"),
    ("201317", "CENTAURI SPECIALTY INSURANCE COMPANY"),
    ("101036", "CENTENNIAL INSURANCE COMPANY"),
    ("100860", "CENTRE INSURANCE COMPANY"),
    ("104710", "CENTURY-NATIONAL INSURANCE COMPANY"),
    ("101913", "CHICAGO INSURANCE COMPANY"),
    ("101167", "CHUBB INDEMNITY INSURANCE COMPANY"),
    ("101165", "CHUBB NATIONAL INSURANCE COMPANY"),
    ("101035", "CHURCH INSURANCE COMPANY"),
    ("102048", "CHURCH MUTUAL INSURANCE COMPANY, S.I."),
    ("101042", "CIM INSURANCE CORPORATION"),
    ("204389", "CIMARRON INSURANCE COMPANY, INC."),
    ("106100", "CITIZENS PROPERTY INSURANCE CORPORATION"),
    ("200439", "CITY NATIONAL INSURANCE COMPANY"),
    ("102439", "CLARENDON NATIONAL INSURANCE COMPANY"),
    ("104523", "CLEAR BLUE INSURANCE COMPANY"),
    ("102295", "CLEAR SPRING PROPERTY AND CASUALTY COMPANY"),
    ("204286", "CM REGENT INSURANCE COMPANY"),
    ("100941", "COALITION INSURANCE COMPANY"),
    ("102474", "COLONIAL AMERICAN CASUALTY AND SURETY COMPANY"),
    ("101518", "COLONY SPECIALTY INSURANCE COMPANY"),
    ("104557", "COLORADO CASUALTY INSURANCE COMPANY"),
    ("102367", "COLUMBIA INSURANCE COMPANY"),
    ("100830", "COMMERCE AND INDUSTRY INSURANCE COMPANY"),
    ("104473", "COMMERCIAL GUARANTY INSURANCE COMPANY"),
    ("101791", "CONCERT INSURANCE COMPANY"),
    ("205459", "CONDO OWNERS RECIPROCAL EXCHANGE"),
    ("100880", "CONSTITUTION INSURANCE COMPANY"),
    ("101682", "CONTINENTAL CASUALTY COMPANY"),
    ("104640", "CONTINENTAL HERITAGE INSURANCE COMPANY"),
    ("101427", "CONTINENTAL INDEMNITY COMPANY"),
    ("100984", "CONTINENTAL INSURANCE COMPANY"),
    ("104668", "CONTRACTORS BONDING &amp; INSURANCE COMPANY"),
    ("105451", "COOPERATIVA DE SEGUROS MULTIPLES DE PUERTO RICO, INC."),
    ("205320", "COPPERPOINT CASUALTY INSURANCE COMPANY"),
    ("205318", "COPPERPOINT PREMIER INSURANCE COMPANY"),
    ("101991", "COREPOINTE INSURANCE COMPANY"),
    ("105320", "CORNERSTONE NATIONAL INSURANCE COMPANY"),
    ("102693", "COTTON STATES MUTUAL INSURANCE COMPANY"),
    ("200916", "COUNTRY MUTUAL INSURANCE COMPANY"),
    ("200919", "COUNTRY PREFERRED INSURANCE COMPANY"),
    ("203071", "CRESTBROOK INSURANCE COMPANY"),
    ("100000", "CROSSROADS INSURANCE COMPANY"),
    ("101156", "CRUM &amp; FORSTER INDEMNITY COMPANY"),
    ("102056", "CUMIS INSURANCE SOCIETY, INC."),
    ("103673", "CYPRESS PROPERTY &amp; CASUALTY INSURANCE COMPANY"),
    ("102060", "DAIRYLAND INSURANCE COMPANY"),
    ("101541", "DEALERS ASSURANCE COMPANY"),
    ("201790", "DEPOSITORS INSURANCE COMPANY"),
    ("102430", "DIAMOND STATE INSURANCE COMPANY"),
    ("100630", "DIGITAL ADVANTAGE INSURANCE COMPANY"),
    ("103799", "DIRECT GENERAL INSURANCE COMPANY"),
    ("200489", "EASTGUARD INSURANCE COMPANY"),
    ("101651", "ECONOMY FIRE &amp; CASUALTY COMPANY"),
    ("101773", "ECONOMY PREFERRED INSURANCE COMPANY"),
    ("105254", "ECONOMY PREMIER ASSURANCE COMPANY"),
    ("201193", "EDISON INSURANCE COMPANY"),
    ("203095", "ELEMENTS PROPERTY INSURANCE COMPANY"),
    ("103814", "EMC PROPERTY &amp; CASUALTY COMPANY"),
    ("102383", "EMPIRE FIRE AND MARINE INSURANCE COMPANY"),
    ("100539", "EMPLOYERS FIRE INSURANCE COMPANY"),
    ("102037", "EMPLOYERS INSURANCE COMPANY OF WAUSAU"),
    ("102173", "EMPLOYERS MUTUAL CASUALTY COMPANY"),
    ("200859", "ENCOMPASS FLORIDIAN INDEMNITY COMPANY"),
    ("200863", "ENCOMPASS FLORIDIAN INSURANCE COMPANY"),
    ("202991", "ENDURANCE AMERICAN INSURANCE COMPANY"),
    ("200732", "ENDURANCE ASSURANCE CORPORATION"),
    ("104880", "ENERGY INSURANCE MUTUAL LIMITED"),
    ("104310", "ESURANCE INSURANCE COMPANY"),
    ("203983", "EVEREST DENALI INSURANCE COMPANY"),
    ("101148", "EVEREST NATIONAL INSURANCE COMPANY"),
    ("203984", "EVEREST PREMIER INSURANCE COMPANY"),
    ("205097", "EVERETT CASH MUTUAL INSURANCE CO."),
    ("101702", "EVERGREEN NATIONAL INDEMNITY COMPANY"),
    ("102065", "EVERSPAN INSURANCE COMPANY"),
    ("100889", "EXECUTIVE RISK INDEMNITY INC."),
    ("100623", "FACTORY MUTUAL INSURANCE COMPANY"),
    ("100934", "FAIR AMERICAN INSURANCE AND REINSURANCE COMPANY"),
    ("104704", "FAIRMONT PREMIER INSURANCE COMPANY"),
    ("104361", "FAIRMONT SPECIALTY INSURANCE COMPANY"),
    ("102204", "FALLS LAKE NATIONAL INSURANCE COMPANY"),
    ("203721", "FAMILY SECURITY INSURANCE COMPANY, INC."),
    ("100627", "FARMERS CASUALTY INSURANCE COMPANY"),
    ("100890", "FARMERS GROUP PROPERTY AND CASUALTY INS. CO."),
    ("104800", "FARMERS INSURANCE EXCHANGE"),
    ("100870", "FARMERS PROPERTY AND CASUALTY INSURANCE CO"),
    ("103264", "FARMERS SPECIALTY INSURANCE COMPANY"),
    ("100701", "FARMINGTON CASUALTY COMPANY"),
    ("102183", "FARMLAND MUTUAL INSURANCE COMPANY"),
    ("201335", "FCCI ADVANTAGE INSURANCE COMPANY"),
    ("103904", "FCCI COMMERCIAL INSURANCE COMPANY"),
    ("102974", "FCCI INSURANCE COMPANY"),
    ("100837", "FEDERAL INSURANCE COMPANY"),
    ("102100", "FEDERATED MUTUAL INSURANCE COMPANY"),
    ("104006", "FEDERATED NATIONAL INSURANCE COMPANY"),
    ("203858", "FEDERATED RESERVE INSURANCE COMPANY"),
    ("102090", "FEDERATED RURAL ELECTRIC INSURANCE EXCHANGE"),
    ("102126", "FEDERATED SERVICE INSURANCE COMPANY"),
    ("103275", "FEDNAT INSURANCE COMPANY"),
    ("100908", "FIDELITY AND DEPOSIT COMPANY OF MARYLAND"),
    ("102208", "FIDELITY AND GUARANTY INSURANCE COMPANY"),
    ("102444", "FIDELITY AND GUARANTY INSURANCE UNDERWRITERS, INC."),
    ("200725", "FIDELITY FIRE &amp; CASUALTY COMPANY"),
    ("104871", "FIRE INSURANCE EXCHANGE"),
    ("104716", "FIREMAN&#39;S FUND INSURANCE COMPANY"),
    ("104736", "FIRST AMERICAN PROPERTY &amp; CASUALTY INSURANCE COMPANY"),
    ("103583", "FIRST COMMUNITY INSURANCE COMPANY"),
    ("101734", "FIRST FINANCIAL INSURANCE COMPANY"),
    ("103637", "FIRST FLORIDIAN AUTO AND HOME INSURANCE COMPANY"),
    ("100599", "FIRST LIBERTY INSURANCE CORPORATION (THE)"),
    ("104662", "FIRST NATIONAL INSURANCE COMPANY OF AMERICA"),
    ("103665", "FIRST PROTECTIVE INSURANCE COMPANY"),
    ("101785", "FLORIDA CASUALTY INSURANCE COMPANY"),
    ("202021", "FLORIDA FAMILY HOME INSURANCE COMPANY"),
    ("103636", "FLORIDA FAMILY INSURANCE COMPANY"),
    ("103014", "FLORIDA FARM BUREAU CASUALTY INSURANCE COMPANY"),
    ("103567", "FLORIDA FARM BUREAU GENERAL INSURANCE COMPANY"),
    ("201068", "FLORIDA PENINSULA INSURANCE COMPANY"),
    ("102377", "FLORIDA SPECIALTY INSURANCE COMPANY"),
    ("101946", "FLORISTS&#39; INSURANCE COMPANY"),
    ("101928", "FLORISTS&#39; MUTUAL INSURANCE COMPANY"),
    ("101597", "FMH AG RISK INSURANCE COMPANY"),
    ("101983", "FOREMOST INSURANCE COMPANY GRAND RAPIDS, MICHIGAN"),
    ("101609", "FOREMOST PROPERTY AND CASUALTY INSURANCE COMPANY"),
    ("102015", "FOREMOST SIGNATURE INSURANCE COMPANY"),
    ("101965", "FRANKENMUTH INSURANCE COMPANY"),
    ("104509", "FREEDOM SPECIALTY INSURANCE COMPANY"),
    ("200895", "GARRISON PROPERTY AND CASUALTY INSURANCE COMPANY"),
    ("104464", "GEICO GENERAL INSURANCE COMPANY"),
    ("102038", "GENERAL CASUALTY COMPANY OF WISCONSIN"),
    ("200831", "GENERAL FIDELITY INSURANCE COMPANY"),
    ("104657", "GENERAL INSURANCE COMPANY OF AMERICA"),
    ("100902", "GENERAL SECURITY NATIONAL INSURANCE COMPANY"),
    ("100834", "GENERAL STAR NATIONAL INSURANCE COMPANY"),
    ("101024", "GENERALI - U. S. BRANCH"),
    ("100689", "GENESIS INSURANCE COMPANY"),
    ("205399", "GEORGIA BUILDERS INSURANCE COMPANY"),
    ("201010", "GEOVERA INSURANCE COMPANY"),
    ("102466", "GLATFELTER INSURANCE COMPANY"),
    ("102363", "GLENCAR INSURANCE COMPANY"),
    ("102527", "GOVERNMENT EMPLOYEES INSURANCE COMPANY"),
    ("202696", "GRANADA INDEMNITY COMPANY"),
    ("103430", "GRANADA INSURANCE COMPANY"),
    ("100487", "GRANITE STATE INSURANCE COMPANY"),
    ("100995", "GRAPHIC ARTS MUTUAL INSURANCE COMPANY"),
    ("104766", "GREAT AMERICAN ALLIANCE INSURANCE COMPANY"),
    ("101064", "GREAT AMERICAN ASSURANCE COMPANY"),
    ("103732", "GREAT AMERICAN CASUALTY INSURANCE COMPANY"),
    ("101897", "GREAT AMERICAN CONTEMPORARY INSURANCE COMPANY"),
    ("101371", "GREAT AMERICAN INSURANCE COMPANY"),
    ("101013", "GREAT AMERICAN INSURANCE COMPANY OF NEW YORK"),
    ("101431", "GREAT AMERICAN SECURITY INSURANCE COMPANY"),
    ("101433", "GREAT AMERICAN SPIRIT INSURANCE COMPANY"),
    ("102330", "GREAT DIVIDE INSURANCE COMPANY"),
    ("104521", "GREAT MIDWEST INSURANCE COMPANY"),
    ("102111", "GREAT NORTHERN INSURANCE COMPANY"),
    ("104765", "GREENWICH INSURANCE COMPANY"),
    ("100838", "GREENWOOD INSURANCE COMPANY"),
    ("101793", "GUIDEONE AMERICA INSURANCE COMPANY"),
    ("102217", "GUIDEONE ELITE INSURANCE COMPANY"),
    ("102186", "GUIDEONE INSURANCE COMPANY"),
    ("102187", "GUIDEONE SPECIALTY INSURANCE COMPANY"),
    ("200888", "GULFSTREAM PROPERTY AND CASUALTY INSURANCE COMPANY"),
    ("100601", "HANOVER AMERICAN INSURANCE COMPANY (THE)"),
    ("100991", "HANOVER INSURANCE COMPANY (THE)"),
    ("101038", "HARCO NATIONAL INSURANCE COMPANY"),
    ("102099", "HARLEYSVILLE INSURANCE COMPANY"),
    ("100645", "HARTFORD ACCIDENT AND INDEMNITY COMPANY"),
    ("100638", "HARTFORD CASUALTY INSURANCE COMPANY"),
    ("100647", "HARTFORD FIRE INSURANCE COMPANY"),
    ("100682", "HARTFORD INSURANCE COMPANY OF THE MIDWEST"),
    ("100685", "HARTFORD INSURANCE COMPANY OF THE SOUTHEAST"),
    ("100648", "HARTFORD STEAM BOILER INSPECTION &amp; INS. COMPANY"),
    ("105528", "HARTFORD STEAM BOILER INSPECTION AND INSURANCE CO. OF CT (THE)"),
    ("100718", "HARTFORD UNDERWRITERS INSURANCE COMPANY"),
    ("201919", "HDI GLOBAL INSURANCE COMPANY"),
    ("101554", "HDI GLOBAL SELECT INSURANCE COMPANY"),
    ("104834", "HERITAGE INDEMNITY COMPANY"),
    ("202847", "HERITAGE PROPERTY &amp; CASUALTY INSURANCE COMPANY"),
    ("201021", "HILLCREST INSURANCE COMPANY"),
    ("104882", "HISCOX  INSURANCE COMPANY INC."),
    ("201559", "HOMEOWNERS CHOICE PROPERTY &amp; CASUALTY INSURANCE COMPANY, INC."),
    ("100707", "HOMESITE INSURANCE COMPANY"),
    ("105200", "HOMESITE INSURANCE COMPANY OF FLORIDA"),
    ("201063", "HOMEWISE INSURANCE COMPANY"),
    ("201196", "HOMEWISE INSURANCE COMPANY, INC."),
    ("201380", "HOMEWISE PREFERRED INSURANCE COMPANY"),
    ("102913", "HORACE MANN INSURANCE COMPANY"),
    ("200898", "HOUSING AUTHORITY PROPERTY INSURANCE, A MUTUAL COMPANY"),
    ("202123", "HOUSING ENTERPRISE INSURANCE COMPANY, INC."),
    ("100993", "HUDSON INSURANCE COMPANY"),
    ("102757", "ILLINOIS INSURANCE COMPANY"),
    ("101932", "ILLINOIS NATIONAL INSURANCE COMPANY"),
    ("201328", "IMPERIAL FIRE AND CASUALTY INSURANCE COMPANY"),
    ("100894", "IMPERIUM INSURANCE COMPANY"),
    ("204285", "INCLINE CASUALTY COMPANY"),
    ("100686", "INDEMNITY INSURANCE COMPANY OF NORTH AMERICA"),
    ("101553", "INDIANA INSURANCE COMPANY"),
    ("101534", "INFINITY INDEMNITY INSURANCE COMPANY"),
    ("101405", "INFINITY INSURANCE COMPANY"),
    ("101182", "INSURANCE COMPANY OF NORTH AMERICA"),
    ("101014", "INSURANCE COMPANY OF THE STATE OF PENNSYLVANIA"),
    ("104809", "INSURANCE COMPANY OF THE WEST"),
    ("204820", "INSUREMAX INSURANCE COMPANY"),
    ("102598", "INTEGON GENERAL INSURANCE CORPORATION"),
    ("102587", "INTEGON INDEMNITY CORPORATION"),
    ("100982", "INTEGON NATIONAL INSURANCE COMPANY"),
    ("100678", "INTEGON PREFERRED INSURANCE COMPANY"),
    ("205139", "INTREPID CASUALTY COMPANY"),
    ("200552", "INTREPID INSURANCE COMPANY"),
    ("102093", "IRONSHORE INDEMNITY  INC."),
    ("101015", "JEFFERSON INSURANCE COMPANY"),
    ("102043", "JEWELERS MUTUAL INSURANCE COMPANY, S.I."),
    ("204219", "JOURNEY INSURANCE COMPANY"),
    ("101907", "KEMPER INDEPENDENCE INSURANCE COMPANY"),
    ("200933", "KEY RISK INSURANCE COMPANY"),
    ("204308", "KIN INTERINSURANCE NETWORK"),
    ("104696", "KIN INTERINSURANCE NEXUS EXCHANGE"),
    ("103366", "KINGSWAY AMIGO INSURANCE COMPANY"),
    ("201993", "KNIGHTBROOK INSURANCE COMPANY"),
    ("100574", "LAMORAK INSURANCE COMPANY"),
    ("101924", "LANCER INSURANCE COMPANY"),
    ("203873", "LEMONADE INSURANCE COMPANY"),
    ("102506", "LEXINGTON NATIONAL INSURANCE CORPORATION"),
    ("103651", "LIBERTY AMERICAN INSURANCE COMPANY"),
    ("103909", "LIBERTY AMERICAN SELECT INSURANCE COMPANY"),
    ("100519", "LIBERTY INSURANCE CORPORATION"),
    ("101131", "LIBERTY INSURANCE UNDERWRITERS INC."),
    ("100557", "LIBERTY MUTUAL FIRE INSURANCE COMPANY"),
    ("100542", "LIBERTY MUTUAL INSURANCE COMPANY"),
    ("101185", "LIBERTY MUTUAL MID-ATLANTIC INSURANCE COMPANY"),
    ("100530", "LIBERTY MUTUAL PERSONAL INSURANCE COMPANY"),
    ("204639", "LIGHTHOUSE PROPERTY INSURANCE CORPORATION"),
    ("104827", "LIO INSURANCE COMPANY"),
    ("100937", "LION INSURANCE COMPANY"),
    ("101130", "LM GENERAL INSURANCE COMPANY"),
    ("100600", "LM INSURANCE CORPORATION"),
    ("101125", "LM PROPERTY AND CASUALTY INSURANCE COMPANY"),
    ("205160", "LOGGERHEAD RECIPROCAL INTERINSURANCE EXCHANGE"),
    ("102253", "LUMBERMEN&#39;S UNDERWRITING ALLIANCE"),
    ("201163", "LYNDON SOUTHERN INSURANCE COMPANY"),
    ("102720", "MAG MUTUAL INSURANCE COMPANY"),
    ("201077", "MAGNOLIA INSURANCE COMPANY"),
    ("200890", "MAIN STREET AMERICA ASSURANCE COMPANY"),
    ("201533", "MAIN STREET AMERICA PROTECTION INSURANCE COMPANY"),
    ("205169", "MAINSAIL INSURANCE COMPANY"),
    ("203685", "MAISON INSURANCE COMPANY"),
    ("205444", "MANATEE INSURANCE EXCHANGE"),
    ("105260", "MANUFACTURERS ALLIANCE INSURANCE COMPANY"),
    ("103935", "MAPFRE INSURANCE COMPANY OF FLORIDA"),
    ("102562", "MARKEL AMERICAN INSURANCE COMPANY"),
    ("100755", "MARKEL GLOBAL REINSURANCE COMPANY"),
    ("101779", "MARKEL INSURANCE COMPANY"),
    ("102440", "MARYLAND CASUALTY COMPANY"),
    ("100561", "MASSACHUSETTS BAY INSURANCE COMPANY"),
    ("101559", "MEDICAL PROTECTIVE COMPANY (THE)"),
    ("102809", "MEDMARC CASUALTY INSURANCE COMPANY"),
    ("102154", "MENDOTA INSURANCE COMPANY"),
    ("103772", "MERASTAR INSURANCE COMPANY"),
    ("104801", "MERCURY CASUALTY COMPANY"),
    ("105430", "MERCURY INDEMNITY COMPANY OF AMERICA"),
    ("105429", "MERCURY INSURANCE COMPANY OF FLORIDA"),
    ("100575", "METROMILE INSURANCE COMPANY"),
    ("101142", "METROPOLITAN GENERAL INSURANCE COMPANY"),
    ("104479", "MGA INSURANCE COMPANY, INC."),
    ("101600", "MIC GENERAL INSURANCE CORPORATION"),
    ("102010", "MIC PROPERTY &amp; CASUALTY INS. CORP."),
    ("104865", "MID-CENTURY INSURANCE COMPANY"),
    ("105475", "MID-CONTINENT ASSURANCE COMPANY"),
    ("105476", "MID-CONTINENT CASUALTY COMPANY"),
    ("100550", "MIDDLESEX INSURANCE COMPANY"),
    ("101738", "MIDVALE INDEMNITY COMPANY"),
    ("101425", "MIDWEST EMPLOYERS CASUALTY COMPANY"),
    ("201284", "MIDWEST FAMILY MUTUAL INSURANCE COMPANY"),
    ("204525", "MILFORD CASUALTY INSURANCE COMPANY"),
    ("200598", "MITSUI SUMITOMO INSURANCE COMPANY OF AMERICA"),
    ("100951", "MITSUI SUMITOMO INSURANCE USA INC."),
    ("201623", "MODERN USA INSURANCE COMPANY"),
    ("203391", "MONARCH NATIONAL INSURANCE COMPANY"),
    ("105581", "MONROE GUARANTY INSURANCE COMPANY"),
    ("202002", "MONTGOMERY MUTUAL INSURANCE COMPANY"),
    ("101973", "MOTORS INSURANCE CORPORATION"),
    ("203127", "MOUNT BEACON INSURANCE COMPANY"),
    ("201377", "MS TRANSVERSE INSURANCE COMPANY"),
    ("102343", "NATIONAL AMERICAN INSURANCE COMPANY"),
    ("104795", "NATIONAL AMERICAN INSURANCE COMPANY OF CALIFORNIA"),
    ("102601", "NATIONAL BUILDERS INSURANCE COMPANY"),
    ("101974", "NATIONAL CASUALTY COMPANY"),
    ("102306", "NATIONAL FIRE AND INDEMNITY EXCHANGE"),
    ("100650", "NATIONAL FIRE INSURANCE COMPANY OF HARTFORD"),
    ("102259", "NATIONAL GENERAL INSURANCE COMPANY"),
    ("102349", "NATIONAL INDEMNITY COMPANY"),
    ("103240", "NATIONAL INDEMNITY COMPANY OF THE SOUTH"),
    ("101526", "NATIONAL INTERSTATE INSURANCE COMPANY"),
    ("103822", "NATIONAL SECURITY FIRE &amp; CASUALTY COMPANY"),
    ("102950", "NATIONAL SPECIALTY INSURANCE COMPANY"),
    ("204865", "NATIONAL SUMMIT INSURANCE COMPANY"),
    ("101735", "NATIONAL SURETY CORPORATION"),
    ("103768", "NATIONAL TRUST INSURANCE COMPANY"),
    ("101343", "NATIONAL UNION FIRE INSURANCE CO. OF PITTSBURGH, PA"),
    ("102388", "NATIONWIDE AFFINITY INSURANCE COMPANY OF AMERICA"),
    ("102203", "NATIONWIDE AGRIBUSINESS INSURANCE COMPANY"),
    ("104754", "NATIONWIDE ASSURANCE COMPANY"),
    ("101468", "NATIONWIDE GENERAL INSURANCE COMPANY"),
    ("104783", "NATIONWIDE INSURANCE COMPANY OF AMERICA"),
    ("101446", "NATIONWIDE INSURANCE COMPANY OF FLORIDA"),
    ("101451", "NATIONWIDE MUTUAL FIRE INSURANCE COMPANY"),
    ("101450", "NATIONWIDE MUTUAL INSURANCE COMPANY"),
    ("101409", "NATIONWIDE PROPERTY AND CASUALTY INSURANCE COMPANY"),
    ("100043", "NAU COUNTRY INSURANCE COMPANY"),
    ("100921", "NAVIGATORS INSURANCE COMPANY"),
    ("100558", "NEW ENGLAND INSURANCE COMPANY"),
    ("104989", "NEW HAMPSHIRE INSURANCE COMPANY"),
    ("100867", "NEW YORK MARINE AND GENERAL INSURANCE COMPANY"),
    ("204650", "NEXT INSURANCE US COMPANY"),
    ("100488", "NGM INSURANCE COMPANY"),
    ("200490", "NORGUARD INSURANCE COMPANY"),
    ("102022", "NORTH POINTE INSURANCE COMPANY"),
    ("101119", "NORTH RIVER INSURANCE COMPANY"),
    ("200884", "NORTHERN CAPITAL INSURANCE COMPANY"),
    ("201688", "NORTHERN CAPITAL SELECT INSURANCE COMPANY"),
    ("100998", "NORTHERN INSURANCE COMPANY OF NEW YORK"),
    ("104748", "NORTHLAND CASUALTY COMPANY"),
    ("102166", "NORTHLAND INSURANCE COMPANY"),
    ("101071", "NOVA CASUALTY COMPANY"),
    ("103773", "OAKWOOD INSURANCE COMPANY"),
    ("204735", "OBSIDIAN INSURANCE COMPANY"),
    ("104550", "OCCIDENTAL FIRE AND CASUALTY COMPANY OF NC"),
    ("100938", "OCEAN HARBOR CASUALTY INSURANCE COMPANY"),
    ("102375", "ODYSSEY REINSURANCE COMPANY"),
    ("101367", "OHIO CASUALTY INSURANCE COMPANY"),
    ("101494", "OHIO FARMERS INSURANCE COMPANY"),
    ("101377", "OHIO INDEMNITY COMPANY"),
    ("105083", "OHIO SECURITY INSURANCE COMPANY"),
    ("103164", "OLD DOMINION INSURANCE COMPANY"),
    ("204696", "OLD GUARD INSURANCE COMPANY"),
    ("101918", "OLD REPUBLIC GENERAL INSURANCE CORPORATION"),
    ("101336", "OLD REPUBLIC INSURANCE COMPANY"),
    ("104323", "OLD REPUBLIC SECURITY ASSURANCE COMPANY"),
    ("201612", "OLYMPUS INSURANCE COMPANY"),
    ("103122", "OMEGA INSURANCE COMPANY"),
    ("205404", "ORANGE INSURANCE EXCHANGE"),
    ("205352", "ORION180 SELECT INSURANCE COMPANY"),
    ("205522", "OVATION HOME INSURANCE EXCHANGE"),
    ("101513", "OWNERS INSURANCE COMPANY"),
    ("104759", "PACIFIC EMPLOYERS INSURANCE COMPANY"),
    ("104760", "PACIFIC INDEMNITY COMPANY"),
    ("105058", "PACIFIC SPECIALTY INSURANCE COMPANY"),
    ("201613", "PALM BEACH WINDSTORM SELF INSURANCE TRUST"),
    ("201008", "PARK NATIONAL INSURANCE COMPANY"),
    ("100548", "PARTNERRE AMERICA INSURANCE COMPANY"),
    ("100570", "PATRIOT GENERAL INSURANCE COMPANY"),
    ("203357", "PATRIOT SELECT PROPERTY AND CASUALTY INSURANCE COMPANY"),
    ("102622", "PEAK PROPERTY AND CASUALTY INSURANCE CORP."),
    ("100892", "PEERLESS INDEMNITY INSURANCE COMPANY"),
    ("100489", "PEERLESS INSURANCE COMPANY"),
    ("103902", "PELEUS INSURANCE COMPANY"),
    ("101333", "PENN MILLERS INSURANCE COMPANY"),
    ("101213", "PENNSYLVANIA INSURANCE COMPANY"),
    ("101193", "PENNSYLVANIA LUMBERMENS MUTUAL INSURANCE COMPANY"),
    ("101237", "PENNSYLVANIA MANUFACTURERS&#39; ASSOCIATION INS. CO."),
    ("105259", "PENNSYLVANIA MANUFACTURERS INDEMNITY COMPANY"),
    ("101194", "PENNSYLVANIA NATIONAL MUTUAL CASUALTY INSURANCE CO"),
    ("201695", "PEOPLE&#39;S TRUST INSURANCE COMPANY"),
    ("202229", "PHARMACISTS MUTUAL INSURANCE COMPANY"),
    ("101244", "PHILADELPHIA INDEMNITY INSURANCE COMPANY"),
    ("101083", "PIE INSURANCE COMPANY (THE)"),
    ("101551", "PINNACLE NATIONAL INSURANCE COMPANY"),
    ("202877", "PLATEAU CASUALTY INSURANCE COMPANY"),
    ("102608", "PLATTE RIVER INSURANCE COMPANY"),
    ("102702", "PLAZA INSURANCE COMPANY"),
    ("100970", "PLYMOUTH ROCK ASSURANCE PREFERRED CORPORATION"),
    ("104687", "POINT SPECIALTY INSURANCE COMPANY"),
    ("101775", "PRAETORIAN INSURANCE COMPANY"),
    ("202041", "PREPARED INSURANCE COMPANY"),
    ("201490", "PRIVILEGE UNDERWRITERS RECIPROCAL EXCHANGE"),
    ("200836", "PRODUCERS AGRICULTURE INSURANCE COMPANY"),
    ("102494", "PROFESSIONALS ADVOCATE INSURANCE COMPANY"),
    ("103757", "PROGRESSIVE ADVANCED INSURANCE COMPANY"),
    ("101511", "PROGRESSIVE AMERICAN INSURANCE COMPANY"),
    ("101542", "PROGRESSIVE CASUALTY INSURANCE COMPANY"),
    ("103584", "PROGRESSIVE EXPRESS INSURANCE COMPANY"),
    ("201643", "PROGRESSIVE PROPERTY INSURANCE COMPANY"),
    ("103135", "PROGRESSIVE SOUTHEASTERN INSURANCE COMPANY"),
    ("100724", "PROPERTY &amp; CASUALTY INSURANCE COMPANY OF HARTFORD"),
    ("101638", "PROTECTIVE INSURANCE COMPANY"),
    ("102272", "PROTECTIVE PROPERTY &amp; CASUALTY INSURANCE COMPANY"),
    ("100795", "PUBLIC SERVICE INSURANCE COMPANY"),
    ("101138", "QBE INSURANCE CORPORATION"),
    ("101707", "R.V.I. AMERICA INSURANCE COMPANY"),
    ("101256", "RADIAN MORTGAGE ASSURANCE INC"),
    ("102091", "REGENT INSURANCE COMPANY"),
    ("205289", "REPUBLIC FIRE AND CASUALTY INSURANCE COMPANY"),
    ("100593", "RESPONSE INSURANCE COMPANY"),
    ("103748", "RESPONSE WORLDWIDE DIRECT AUTO INSURANCE COMPANY"),
    ("102075", "RESPONSE WORLDWIDE INSURANCE COMPANY"),
    ("102156", "RIVERPORT INSURANCE COMPANY"),
    ("100569", "RIVERSTONE INTERNATIONAL INSURANCE, INC."),
    ("101944", "RLI INSURANCE COMPANY"),
    ("205278", "ROCHDALE INSURANCE COMPANY"),
    ("204422", "ROCK RIDGE INSURANCE COMPANY"),
    ("101594", "ROCKWOOD CASUALTY INSURANCE COMPANY"),
    ("204629", "ROOT INSURANCE COMPANY"),
    ("202700", "ROOT PROPERTY &amp; CASUALTY INSURANCE COMPANY"),
    ("101066", "RSUI INDEMNITY COMPANY"),
    ("201237", "RURAL COMMUNITY INSURANCE COMPANY"),
    ("104504", "RURAL TRUST INSURANCE COMPANY"),
    ("201255", "SAFE HARBOR INSURANCE COMPANY"),
    ("104664", "SAFECO INSURANCE COMPANY OF AMERICA"),
    ("203106", "SAFEPOINT INSURANCE COMPANY"),
    ("103092", "SAFEPORT INSURANCE COMPANY"),
    ("102248", "SAFETY NATIONAL CASUALTY CORPORATION"),
    ("202497", "SAMSUNG FIRE &amp; MARINE INSURANCE CO., LTD. (US BRANCH)"),
    ("201594", "SAWGRASS MUTUAL INSURANCE COMPANY"),
    ("101423", "SCOTTSDALE INDEMNITY COMPANY"),
    ("201172", "SECURIAN CASUALTY COMPANY"),
    ("200981", "SECURITY FIRST INSURANCE COMPANY"),
    ("104510", "SELECT INSURANCE COMPANY"),
    ("102614", "SELECTIVE INSURANCE COMPANY OF THE SOUTHEAST"),
    ("100896", "SENECA INSURANCE COMPANY, INC."),
    ("105225", "SENTINEL INSURANCE COMPANY, LTD."),
    ("200807", "SENTRY CASUALTY COMPANY"),
    ("102039", "SENTRY INSURANCE COMPANY"),
    ("101730", "SENTRY SELECT INSURANCE COMPANY"),
    ("103159", "SERVICE AMERICAN INDEMNITY COMPANY"),
    ("102695", "SHIELD INSURANCE COMPANY"),
    ("100900", "SIRIUSPOINT AMERICA INSURANCE COMPANY"),
    ("205025", "SLIDE INSURANCE COMPANY"),
    ("100879", "SOMPO AMERICA FIRE &amp; MARINE INSURANCE COMPANY"),
    ("100848", "SOMPO AMERICA INSURANCE COMPANY"),
    ("103852", "SOUTHERN FARM BUREAU CASUALTY INSURANCE COMPANY"),
    ("200989", "SOUTHERN FIDELITY INSURANCE COMPANY"),
    ("202686", "SOUTHERN FIDELITY PROPERTY &amp; CASUALTY, INC."),
    ("104516", "SOUTHERN INSURANCE COMPANY"),
    ("200925", "SOUTHERN OAK INSURANCE COMPANY"),
    ("204895", "SOUTHERN VANGUARD INSURANCE COMPANY"),
    ("103601", "SOUTHERN-OWNERS INSURANCE COMPANY"),
    ("100531", "SPARTA INSURANCE COMPANY"),
    ("203673", "SPINNAKER INSURANCE COMPANY"),
    ("200699", "ST. JOHNS INSURANCE COMPANY, INC."),
    ("102098", "ST. PAUL FIRE AND MARINE INSURANCE COMPANY"),
    ("102124", "ST. PAUL GUARDIAN INSURANCE COMPANY"),
    ("102119", "ST. PAUL MERCURY INSURANCE COMPANY"),
    ("101712", "ST. PAUL PROTECTIVE INSURANCE COMPANY"),
    ("102730", "STANDARD GUARANTY INSURANCE COMPANY"),
    ("202060", "STAR &amp; SHIELD INSURANCE EXCHANGE"),
    ("102020", "STAR INSURANCE COMPANY"),
    ("101172", "STARNET INSURANCE COMPANY"),
    ("104472", "STARR INDEMNITY &amp; LIABILITY COMPANY"),
    ("104762", "STARSTONE NATIONAL INSURANCE COMPANY"),
    ("102671", "STATE AUTO PROPERTY &amp; CASUALTY INSURANCE COMPANY"),
    ("101460", "STATE AUTOMOBILE MUTUAL INSURANCE COMPANY"),
    ("101934", "STATE FARM FIRE AND CASUALTY COMPANY"),
    ("101910", "STATE FARM FLORIDA INSURANCE COMPANY"),
    ("101941", "STATE FARM GENERAL INSURANCE COMPANY"),
    ("104486", "STATE NATIONAL INSURANCE COMPANY INC."),
    ("200241", "STILLWATER INSURANCE COMPANY"),
    ("101068", "STILLWATER PROPERTY AND CASUALTY INSURANCE COMPANY"),
    ("203992", "STONEWOOD INSURANCE COMPANY"),
    ("102646", "STONINGTON INSURANCE COMPANY"),
    ("100500", "STRATFORD INSURANCE COMPANY"),
    ("103661", "SUNSHINE STATE INSURANCE COMPANY"),
    ("102665", "SUSSEX INSURANCE COMPANY"),
    ("104666", "SUTTON NATIONAL INSURANCE COMPANY"),
    ("100498", "SWISS RE CORPORATE SOLUTIONS AMERICA INSURANCE CORPORATION"),
    ("100949", "SWISS RE CORPORATE SOLUTIONS ELITE INSURANCE CORPORATION"),
    ("101762", "SWISS RE CORPORATE SOLUTIONS PREMIER INSURANCE CORPORATION"),
    ("100571", "T.H.E. INSURANCE COMPANY"),
    ("205614", "TAILROW INSURANCE EXCHANGE"),
    ("101246", "TEACHERS INSURANCE COMPANY"),
    ("200551", "TECHNOLOGY INSURANCE COMPANY, INC"),
    ("104866", "TESLA INSURANCE COMPANY"),
    ("104782", "TESLA PROPERTY &amp; CASUALTY, INC."),
    ("100670", "THE AUTOMOBILE INSURANCE COMPANY OF HARTFORD, CONNECTICUT"),
    ("100637", "THE CHARTER OAK FIRE INSURANCE COMPANY"),
    ("101392", "THE CINCINNATI CASUALTY COMPANY"),
    ("101435", "THE CINCINNATI INDEMNITY COMPANY"),
    ("101375", "THE CINCINNATI INSURANCE COMPANY"),
    ("100639", "THE PHOENIX INSURANCE COMPANY"),
    ("100763", "THE STANDARD FIRE INSURANCE COMPANY"),
    ("102142", "THE TRAVELERS CASUALTY COMPANY"),
    ("100654", "THE TRAVELERS INDEMNITY COMPANY"),
    ("102791", "THE TRAVELERS INDEMNITY COMPANY OF AMERICA"),
    ("100643", "THE TRAVELERS INDEMNITY COMPANY OF CONNECTICUT"),
    ("104713", "TIG INSURANCE COMPANY"),
    ("104393", "TITAN INDEMNITY COMPANY"),
    ("200899", "TNUS INSURANCE COMPANY"),
    ("204575", "TOGGLE INSURANCE COMPANY"),
    ("202918", "TOKIO MARINE AMERICA INSURANCE COMPANY"),
    ("101039", "TOKIO MARINE AND NICHIDO FIRE INS. CO., LTD. (US BRANCH)"),
    ("204981", "TOWER HILL INSURANCE EXCHANGE"),
    ("102625", "TOWER HILL PREFERRED INSURANCE COMPANY"),
    ("105063", "TOWER HILL PRIME INSURANCE COMPANY"),
    ("200761", "TOWER HILL SELECT INSURANCE COMPANY"),
    ("201305", "TOWER HILL SIGNATURE INSURANCE COMPANY"),
    ("100917", "TRANS PACIFIC INSURANCE COMPANY"),
    ("101833", "TRANSGUARD INSURANCE COMPANY OF AMERICA, INC."),
    ("101676", "TRANSPORTATION INSURANCE COMPANY"),
    ("101625", "TRAVCO INSURANCE COMPANY"),
    ("101771", "TRAVCO PERSONAL INSURANCE COMPANY"),
    ("100762", "TRAVELERS CASUALTY AND SURETY COMPANY"),
    ("100677", "TRAVELERS CASUALTY AND SURETY COMPANY OF AMERICA"),
    ("100728", "TRAVELERS CASUALTY COMPANY OF CONNECTICUT"),
    ("100672", "TRAVELERS CASUALTY INSURANCE COMPANY OF AMERICA"),
    ("104835", "TRAVELERS COMMERCIAL CASUALTY COMPANY"),
    ("100729", "TRAVELERS COMMERCIAL INSURANCE COMPANY"),
    ("101626", "TRAVELERS HOME AND MARINE INSURANCE COMPANY(THE)"),
    ("101741", "TRAVELERS PROPERTY CASUALTY COMPANY OF AMERICA"),
    ("100731", "TRAVELERS PROPERTY CASUALTY INSURANCE COMPANY"),
    ("205564", "TRIDENT RECIPROCAL EXCHANGE"),
    ("203678", "TRI-STATE INSURANCE COMPANY OF MINNESOTA"),
    ("104806", "TRISURA INSURANCE COMPANY"),
    ("103195", "TRITON INSURANCE COMPANY"),
    ("202278", "TRIUMPHE CASUALTY COMPANY"),
    ("104799", "TRUCK INSURANCE EXCHANGE"),
    ("204638", "TRUSTED RESOURCE UNDERWRITERS EXCHANGE"),
    ("100665", "TWIN CITY FIRE INSURANCE COMPANY"),
    ("203626", "TYPTAP INSURANCE COMPANY"),
    ("102497", "U.S. SPECIALTY INSURANCE COMPANY"),
    ("203089", "U.S. UNDERWRITERS INSURANCE COMPANY"),
    ("104357", "UFG SPECIALTY INSURANCE COMPANY"),
    ("203461", "UNION INSURANCE COMPANY"),
    ("201726", "UNIQUE INSURANCE COMPANY"),
    ("205398", "UNITED BUILDERS INSURANCE COMPANY"),
    ("101225", "UNITED CASUALTY INSURANCE COMPANY OF AMERICA"),
    ("101794", "UNITED FINANCIAL CASUALTY COMPANY"),
    ("104412", "UNITED FIRE &amp; INDEMNITY COMPANY"),
    ("102185", "UNITED FIRE AND CASUALTY COMPANY"),
    ("105012", "UNITED PROPERTY &amp; CASUALTY INSURANCE COMPANY"),
    ("104355", "UNITED SERVICES AUTOMOBILE ASSOCIATION"),
    ("102443", "UNITED STATES FIDELITY AND GUARANTY COMPANY"),
    ("101006", "UNITED STATES FIRE INSURANCE COMPANY"),
    ("101207", "UNITED STATES LIABILITY INSURANCE COMPANY"),
    ("102508", "UNITRIN AUTO AND HOME INSURANCE COMPANY"),
    ("200803", "UNIVERSAL FIRE &amp; CASUALTY INSURANCE COMPANY"),
    ("200400", "UNIVERSAL INSURANCE COMPANY"),
    ("200786", "UNIVERSAL INSURANCE COMPANY OF NORTH AMERICA"),
    ("204046", "UNIVERSAL NORTH AMERICA INSURANCE COMPANY"),
    ("104219", "UNIVERSAL PROPERTY &amp; CASUALTY INSURANCE COMPANY"),
    ("102287", "UNIVERSAL UNDERWRITERS INSURANCE COMPANY"),
    ("203546", "US COASTAL PROPERTY &amp; CASUALTY INSURANCE COMPANY"),
    ("103529", "USAA CASUALTY INSURANCE COMPANY"),
    ("104375", "USAA GENERAL INDEMNITY COMPANY"),
    ("200605", "USIC OF FLORIDA, INC."),
    ("101061", "UTICA FIRST INSURANCE COMPANY"),
    ("101062", "UTICA MUTUAL INSURANCE COMPANY"),
    ("101229", "VALLEY FORGE INSURANCE COMPANY"),
    ("104580", "VANLINER INSURANCE COMPANY"),
    ("101758", "VANTAGE RISK ASSURANCE COMPANY"),
    ("203627", "VANTAPRO SPECIALTY INSURANCE COMPANY"),
    ("203919", "VAULT RECIPROCAL EXCHANGE"),
    ("202018", "VERLAN FIRE INSURANCE COMPANY"),
    ("101522", "VICTORIA FIRE &amp; CASUALTY COMPANY"),
    ("100836", "VIGILANT INSURANCE COMPANY"),
    ("101790", "VIRGINIA SURETY COMPANY, INC."),
    ("204987", "VYRD INSURANCE COMPANY"),
    ("101811", "WARNER INSURANCE COMPANY"),
    ("101830", "WAUSAU BUSINESS INSURANCE COMPANY"),
    ("102076", "WAUSAU UNDERWRITERS INSURANCE COMPANY"),
    ("101105", "WCF SELECT INSURANCE COMPANY"),
    ("104846", "WELLFLEET INSURANCE COMPANY"),
    ("104576", "WESCO INSURANCE COMPANY"),
    ("101378", "WEST AMERICAN INSURANCE COMPANY"),
    ("104690", "WESTCHESTER FIRE INSURANCE COMPANY"),
    ("101543", "WESTFIELD INSURANCE COMPANY"),
    ("101507", "WESTFIELD NATIONAL INS. COMPANY"),
    ("205233", "WESTGUARD INSURANCE COMPANY"),
    ("202707", "WESTON INSURANCE COMPANY"),
    ("201213", "WESTON PROPERTY &amp; CASUALTY INSURANCE COMPANY"),
    ("102403", "WESTPORT INSURANCE CORPORATION"),
    ("202857", "WHITE PINE INSURANCE COMPANY"),
    ("104757", "WORKMEN&#39;S AUTO INSURANCE COMPANY"),
    ("202006", "WRIGHT NATIONAL FLOOD INSURANCE COMPANY"),
    ("104512", "XL INSURANCE AMERICA, INC."),
    ("100797", "XL REINSURANCE AMERICA INC."),
    ("104577", "XL SPECIALTY INSURANCE COMPANY"),
    ("104715", "YOSEMITE INSURANCE COMPANY"),
    ("104769", "ZENITH INSURANCE COMPANY"),
    ("101908", "ZURICH AMERICAN INSURANCE COMPANY"),
    ("101752", "ZURICH AMERICAN INSURANCE COMPANY OF ILLINOIS"),
]

counties = [
    ("11", "Alachua"),
    ("52", "Baker"),
    ("23", "Bay"),
    ("45", "Bradford"),
    ("19", "Brevard"),
    ("10", "Broward"),
    ("58", "Calhoun"),
    ("53", "Charlotte"),
    ("47", "Citrus"),
    ("48", "Clay"),
    ("64", "Collier"),
    ("29", "Columbia"),
    ("01", "Dade"),
    ("34", "Desoto"),
    ("54", "Dixie"),
    ("02", "Duval"),
    ("09", "Escambia"),
    ("61", "Flagler"),
    ("59", "Franklin"),
    ("21", "Gadsden"),
    ("55", "Gilchrist"),
    ("60", "Glades"),
    ("66", "Gulf"),
    ("56", "Hamilton"),
    ("30", "Hardee"),
    ("49", "Hendry"),
    ("40", "Hernando"),
    ("27", "Highlands"),
    ("03", "Hillsborough"),
    ("51", "Holmes"),
    ("32", "Indian River"),
    ("25", "Jackson"),
    ("46", "Jefferson"),
    ("62", "Lafayette"),
    ("12", "Lake"),
    ("18", "Lee"),
    ("13", "Leon"),
    ("39", "Levy"),
    ("67", "Liberty"),
    ("35", "Madison"),
    ("15", "Manatee"),
    ("14", "Marion"),
    ("42", "Martin"),
    ("38", "Monroe"),
    ("41", "Nassau"),
    ("43", "Okaloosa"),
    ("57", "Okeechobee"),
    ("07", "Orange"),
    ("26", "Osceola"),
    ("00", "Out of state"),
    ("06", "Palm Beach"),
    ("28", "Pasco"),
    ("04", "Pinellas"),
    ("05", "Polk"),
    ("22", "Putnam"),
    ("33", "Santa Rosa"),
    ("16", "Sarasota"),
    ("17", "Seminole"),
    ("20", "St. Johns"),
    ("24", "St. Lucie"),
    ("44", "Sumter"),
    ("31", "Suwannee"),
    ("37", "Taylor"),
    ("63", "Union"),
    ("08", "Volusia"),
    ("65", "Wakulla"),
    ("36", "Walton"),
    ("50", "Washington"),
]

print(f"🔧 修复版本：将运行 {len(insurers)} 家公司 × {len(counties)} 个县，共 {len(insurers)*len(counties)} 次")

# ==== 启动 Selenium ChromeDriver ====
service = Service(CHROME_DRIVER_PATH)
options = webdriver.ChromeOptions()
options.add_experimental_option("prefs", {
    "download.default_directory": DOWNLOAD_DIR,
    "download.prompt_for_download": False,
})

driver = webdriver.Chrome(service=service, options=options)
driver.execute_cdp_cmd("Page.setDownloadBehavior", {"behavior": "allow", "downloadPath": DOWNLOAD_DIR})
wait = WebDriverWait(driver, 30)

try:
    # 1. 打开查询条件页面
    driver.get("https://apps.fldfs.com/QSRNG/Reports/ReportCriteria.aspx")
    driver.maximize_window()

    # 2. 基本设置
    wait.until(EC.element_to_be_clickable((By.ID, "ctl00_ContentPlaceHolder1_radReportType_2"))).click()
    time.sleep(0.3)
    
    Select(wait.until(EC.presence_of_element_located((By.ID, "ctl00_ContentPlaceHolder1_ddlFilingType")))).select_by_value("1")
    time.sleep(0.3)
    
    wait.until(EC.element_to_be_clickable((By.ID, "ctl00_ContentPlaceHolder1_lstReportingPeriod_MoveAllRight"))).click()
    time.sleep(0.3)

    # ==== 主循环：Insurer × County ====
    for ins_val, ins_name in insurers:
        # 选择保险公司
        driver.find_element(By.ID, "ctl00_ContentPlaceHolder1_lstInsurers_MoveLeft").click()
        time.sleep(2)
        driver.find_element(By.CSS_SELECTOR, f"#ctl00_ContentPlaceHolder1_lstInsurers option[value='{ins_val}']").click()
        driver.find_element(By.ID, "ctl00_ContentPlaceHolder1_lstInsurers_MoveRight").click()
        time.sleep(2)
        print(f"[公司] {ins_name}")

        # 全选选项
        driver.find_element(By.ID, "ctl00_ContentPlaceHolder1_lstPolicyType_MoveAllRight").click()
        time.sleep(2)
        driver.find_element(By.ID, "ctl00_ContentPlaceHolder1_lstDataElements_MoveAllRight").click()
        time.sleep(2)

        for ct_val, ct_name in counties:
            # 选择县区
            driver.find_element(By.ID, "ctl00_ContentPlaceHolder1_lstCounties_MoveLeft").click()
            time.sleep(2)
            driver.find_element(By.CSS_SELECTOR, f"#ctl00_ContentPlaceHolder1_lstCounties option[value='{ct_val}']").click()
            time.sleep(2)
            driver.find_element(By.ID, "ctl00_ContentPlaceHolder1_lstCounties_MoveRight").click()
            time.sleep(2)
            print(f"  [县] {ct_name}")
            
            # ==== 搜索和导出 - 增强版窗口等待 ====
            driver.find_element(By.ID, "ctl00_ContentPlaceHolder1_btnSearch").click()
            main_handle = driver.current_window_handle
            
            # 🚀 使用智能重试机制等待新窗口
            window_opened = False
            for attempt in range(3):  # 最多重试3次
                try:
                    print(f"    🔄 等待新窗口 (第{attempt+1}/3次)")
                    
                    for i in range(45):  # 每次尝试等待45秒
                        current_windows = len(driver.window_handles)
                        if current_windows > 1:
                            print(f"    ✅ 第{i+1}秒检测到新窗口！")
                            new_handle = [h for h in driver.window_handles if h != main_handle][0]
                            driver.switch_to.window(new_handle)
                            window_opened = True
                            break
                        
                        if i % 5 == 0:  # 每5秒显示一次进度
                            print(f"    ⏳ 等待中... ({i+1}/45秒)")
                        time.sleep(1)
                    
                    if window_opened:
                        break
                    else:
                        print(f"    ⚠️ 第{attempt+1}次尝试超时")
                        if attempt < 2:  # 还有重试机会
                            print(f"    🔄 准备重试...")
                            time.sleep(3)
                            
                except Exception as e:
                    print(f"    ❌ 第{attempt+1}次尝试出错: {e}")
                    if attempt < 2:
                        time.sleep(3)
                        
            if not window_opened:
                print(f"    ❌ 所有重试都失败，跳过 {ins_name} — {ct_name}")
                continue
            
            # 等待页面加载完成
            wait.until(lambda d: d.execute_script("return document.readyState") == "complete")
            print(f"    📄 页面基础加载完成")
            
            # 🚀 关键改进：等待导出按钮激活
            print(f"    ⏳ 等待导出功能激活...")
            
            def is_export_button_enabled():
                try:
                    export_img = driver.find_element(By.ID, "rptViewer_ctl05_ctl04_ctl00_ButtonImg")
                    src = export_img.get_attribute("src")
                    return "ExportDisabled" not in src
                except:
                    return False
            
            # 等待导出按钮激活
            button_activated = False
            for i in range(60):
                if is_export_button_enabled():
                    button_activated = True
                    print(f"    ✅ 导出按钮已激活")
                    break
                if i % 10 == 0:
                    print(f"    ⏳ 等待激活... ({i+1}/60秒)")
                time.sleep(1)
            
            if not button_activated:
                print(f"    ⚠️ 按钮未激活，尝试强制操作")
            
            # 🎯 尝试导出
            export_success = False
            
            # 方法1：JavaScript API
            try:
                print(f"    🎯 方法1：直接API调用...")
                result = driver.execute_script("""
                    try {
                        var reportViewer = $find('rptViewer');
                        if (reportViewer) {
                            reportViewer.exportReport('CSV');
                            return 'success';
                        } else {
                            return 'reportViewer_not_found';
                        }
                    } catch (e) {
                        return 'error: ' + e.message;
                    }
                """)
                
                if result == 'success':
                    export_success = True
                    print(f"    ✅ 方法1成功")
                else:
                    print(f"    ❌ 方法1失败: {result}")
                    
            except Exception as e:
                print(f"    ❌ 方法1异常: {e}")
            
            # 方法2：模拟点击
            if not export_success:
                try:
                    print(f"    🎯 方法2：模拟点击...")
                    export_btn = driver.find_element(By.ID, "rptViewer_ctl05_ctl04_ctl00_ButtonLink")
                    driver.execute_script("arguments[0].click();", export_btn)
                    time.sleep(2)
                    
                    # 查找CSV选项
                    csv_selectors = [
                        "//a[@title='CSV (comma delimited)']",
                        "//a[contains(text(), 'CSV')]",
                        "//a[contains(@onclick, 'CSV')]"
                    ]
                    
                    csv_found = False
                    for selector in csv_selectors:
                        for wait_time in range(10):
                            try:
                                csv_item = driver.find_element(By.XPATH, selector)
                                driver.execute_script("arguments[0].click();", csv_item)
                                export_success = True
                                csv_found = True
                                print(f"    ✅ 方法2成功")
                                break
                            except:
                                time.sleep(0.5)
                        if csv_found:
                            break
                    
                    if not csv_found:
                        print(f"    ❌ 未找到CSV选项")
                        
                except Exception as e:
                    print(f"    ❌ 方法2失败: {e}")
            
            # 处理下载结果
            if export_success:
                print(f"    ⏳ 等待下载完成...")
                time.sleep(8)
                
                try:
                    download_files = [f for f in os.listdir(DOWNLOAD_DIR) if f.endswith('.csv')]
                    if download_files:
                        latest_file = max([os.path.join(DOWNLOAD_DIR, f) for f in download_files], key=os.path.getctime)
                        new_filename = f"{ins_name}_{ct_name}.csv"
                        new_path = os.path.join(DOWNLOAD_DIR, new_filename)
                        
                        if latest_file != new_path:
                            os.rename(latest_file, new_path)
                        print(f"    ✅ 下载成功: {new_filename}")
                    else:
                        print(f"    ❌ 未检测到下载文件")
                except Exception as e:
                    print(f"    ⚠️ 检查下载出错: {e}")
            else:
                print(f"    ❌ 所有导出方法都失败，跳过 {ins_name} — {ct_name}")
            
            # 关闭标签页，返回主窗口
            driver.close()
            driver.switch_to.window(main_handle)
            wait.until(EC.element_to_be_clickable((By.ID, "ctl00_ContentPlaceHolder1_lstReportingPeriod_MoveAllRight")))
            print(f"    ✔ 完成：{ins_name} — {ct_name}")

finally:
    driver.quit()
    print("\n🎉 程序执行完成！")






# 🔧 问题诊断版本：分析新窗口打开问题
# 这个版本会帮助我们了解为什么新窗口没有打开

import time
import os
from selenium import webdriver
from selenium.webdriver.chrome.service import Service
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait, Select
from selenium.webdriver.support import expected_conditions as EC
from selenium.common.exceptions import TimeoutException

# 配置
CHROME_DRIVER_PATH = "/Users/<USER>/Documents/赚点小银子/编程/400/chromedriver"
DOWNLOAD_DIR = "/Users/<USER>/Documents/赚点小银子/编程/400/QSRNG_reports"

# 测试数据 - 只测试一家公司的一个县
test_insurer = ("101268", "21ST CENTURY CENTENNIAL INSURANCE COMPANY")
test_county = ("34", "Desoto")

print(f"🔍 诊断模式：测试 {test_insurer[1]} - {test_county[1]}")

# 启动 Selenium ChromeDriver
service = Service(CHROME_DRIVER_PATH)
options = webdriver.ChromeOptions()
options.add_experimental_option("prefs", {
    "download.default_directory": DOWNLOAD_DIR,
    "download.prompt_for_download": False,
})

driver = webdriver.Chrome(service=service, options=options)
driver.execute_cdp_cmd("Page.setDownloadBehavior", {"behavior": "allow", "downloadPath": DOWNLOAD_DIR})
wait = WebDriverWait(driver, 30)

try:
    # 1. 打开查询条件页面
    print("🌐 打开网站...")
    driver.get("https://apps.fldfs.com/QSRNG/Reports/ReportCriteria.aspx")
    driver.maximize_window()
    time.sleep(3)  # 等待页面完全加载

    # 2. 基本设置
    print("⚙️ 配置查询条件...")
    wait.until(EC.element_to_be_clickable((By.ID, "ctl00_ContentPlaceHolder1_radReportType_2"))).click()
    time.sleep(1)
    
    Select(wait.until(EC.presence_of_element_located((By.ID, "ctl00_ContentPlaceHolder1_ddlFilingType")))).select_by_value("1")
    time.sleep(1)
    
    wait.until(EC.element_to_be_clickable((By.ID, "ctl00_ContentPlaceHolder1_lstReportingPeriod_MoveAllRight"))).click()
    time.sleep(1)

    # 3. 选择保险公司
    print(f"🏢 选择保险公司: {test_insurer[1]}")
    driver.find_element(By.ID, "ctl00_ContentPlaceHolder1_lstInsurers_MoveLeft").click()
    time.sleep(2)
    driver.find_element(By.CSS_SELECTOR, f"#ctl00_ContentPlaceHolder1_lstInsurers option[value='{test_insurer[0]}']").click()
    driver.find_element(By.ID, "ctl00_ContentPlaceHolder1_lstInsurers_MoveRight").click()
    time.sleep(2)

    # 4. 全选选项
    print("✅ 全选相关选项...")
    driver.find_element(By.ID, "ctl00_ContentPlaceHolder1_lstPolicyType_MoveAllRight").click()
    time.sleep(2)
    driver.find_element(By.ID, "ctl00_ContentPlaceHolder1_lstDataElements_MoveAllRight").click()
    time.sleep(2)

    # 5. 选择县区
    print(f"📍 选择县区: {test_county[1]}")
    driver.find_element(By.ID, "ctl00_ContentPlaceHolder1_lstCounties_MoveLeft").click()
    time.sleep(2)
    driver.find_element(By.CSS_SELECTOR, f"#ctl00_ContentPlaceHolder1_lstCounties option[value='{test_county[0]}']").click()
    time.sleep(2)
    driver.find_element(By.ID, "ctl00_ContentPlaceHolder1_lstCounties_MoveRight").click()
    time.sleep(2)

    # 6. 点击搜索前检查当前窗口数量
    current_windows = len(driver.window_handles)
    print(f"🔍 搜索前窗口数量: {current_windows}")
    print(f"🔍 当前URL: {driver.current_url}")
    
    # 7. 点击搜索按钮
    print("🔍 点击搜索按钮...")
    search_button = driver.find_element(By.ID, "ctl00_ContentPlaceHolder1_btnSearch")
    main_handle = driver.current_window_handle
    print(f"🔍 主窗口句柄: {main_handle}")
    
    # 点击搜索
    search_button.click()
    print("✅ 已点击搜索按钮，等待响应...")
    
    # 8. 监控窗口变化
    for i in range(30):  # 等待30秒
        current_windows = len(driver.window_handles)
        current_url = driver.current_url
        print(f"⏱️ 第{i+1}秒 - 窗口数: {current_windows}, URL: {current_url}")
        
        if current_windows > 1:
            print("✅ 检测到新窗口打开！")
            new_handle = [h for h in driver.window_handles if h != main_handle][0]
            print(f"🔍 新窗口句柄: {new_handle}")
            driver.switch_to.window(new_handle)
            print(f"🔍 新窗口URL: {driver.current_url}")
            break
        elif current_url != "https://apps.fldfs.com/QSRNG/Reports/ReportCriteria.aspx":
            print(f"⚠️ 页面跳转到: {current_url}")
            # 页面在同一窗口中跳转，不是新窗口
            break
        
        time.sleep(1)
    else:
        print("❌ 超时：30秒内没有检测到新窗口或页面跳转")
        print("🔍 最终调试信息:")
        print(f"   - 窗口数量: {len(driver.window_handles)}")
        print(f"   - 当前URL: {driver.current_url}")
        print(f"   - 页面标题: {driver.title}")
        
        # 保存页面源码用于调试
        with open("debug_search_result.html", "w", encoding="utf-8") as f:
            f.write(driver.page_source)
        print("💾 页面源码已保存到 debug_search_result.html")
        
        raise TimeoutException("搜索后没有打开新窗口或跳转页面")

except Exception as e:
    print(f"❌ 发生错误: {e}")
    print(f"🔍 当前窗口数量: {len(driver.window_handles)}")
    print(f"🔍 当前URL: {driver.current_url}")
    
finally:
    # 不要立即关闭，让用户能看到结果
    input("📋 按回车键继续关闭浏览器...")
    driver.quit()
    print("🎉 诊断完成！")


# 🚀 增强版主程序：包含智能重试机制
# 解决偶发的新窗口超时问题

import time
import os
from selenium import webdriver
from selenium.webdriver.chrome.service import Service
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait, Select
from selenium.webdriver.support import expected_conditions as EC
from selenium.common.exceptions import TimeoutException

# 配置
CHROME_DRIVER_PATH = "/Users/<USER>/Documents/赚点小银子/编程/400/chromedriver"
DOWNLOAD_DIR = "/Users/<USER>/Documents/赚点小银子/编程/400/QSRNG_reports"

# 智能窗口等待函数
def wait_for_new_window_with_retry(driver, main_handle, max_attempts=3, timeout_per_attempt=45):
    """智能等待新窗口打开，包含重试机制"""
    for attempt in range(max_attempts):
        try:
            print(f"    🔄 尝试等待新窗口 (第{attempt+1}/{max_attempts}次)")
            
            for i in range(timeout_per_attempt):
                current_windows = len(driver.window_handles)
                current_url = driver.current_url
                
                if current_windows > 1:
                    print(f"    ✅ 第{i+1}秒检测到新窗口！")
                    new_handle = [h for h in driver.window_handles if h != main_handle][0]
                    driver.switch_to.window(new_handle)
                    return True
                elif current_url != "https://apps.fldfs.com/QSRNG/Reports/ReportCriteria.aspx":
                    print(f"    ⚠️ 页面跳转到: {current_url}")
                    return True
                
                if i % 5 == 0:  # 每5秒显示一次进度
                    print(f"    ⏳ 等待中... ({i+1}/{timeout_per_attempt}秒)")
                
                time.sleep(1)
            
            print(f"    ⚠️ 第{attempt+1}次尝试超时")
            if attempt < max_attempts - 1:
                print(f"    🔄 准备重试...")
                time.sleep(2)
                
        except Exception as e:
            print(f"    ❌ 第{attempt+1}次尝试出错: {e}")
            if attempt < max_attempts - 1:
                time.sleep(2)
    
    return False

print("🚀 准备启动增强版程序...")


